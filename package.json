{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@nuxt/image": "^1.10.0", "apexcharts": "^3.41.0", "axios": "^1.11.0", "csv-parse": "^6.1.0", "formidable": "^3.5.4", "locomotive-scroll": "^4.1.4", "mysql2": "^3.14.3", "node-cache": "^5.1.2", "nuxt": "^4.0.1", "papaparse": "5.5.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.4.0"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0"}}