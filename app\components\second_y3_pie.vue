<script setup>
import VueApexCharts from 'vue3-apexcharts';

const series = [70];

const chartOptions = {
  chart: {
    height: 350,
    type: 'radialBar'
  },
  plotOptions: {
    radialBar: {
      hollow: {
        size: '50%'
      },
      track: {
        strokeWidth: '100%'
      },
      dataLabels: {
        name: {
          show: false
        },
        value: {
          show: true,
          fontSize: '24px',
          fontWeight: 600,
          color: '#1E293B'
        }
      }
    }
  },
  fill: {
    colors: ['#374151']
  },
  labels: []
};
</script>

<template>
  <client-only>
    <div class="flex justify-center p-10">
      <VueApexCharts
        type="radialBar"
        :options="chartOptions"
        :series="series"
        height="200"
      />
    </div>
  </client-only>
</template>
